import sys
from typing import Dict, Tuple, Optional
from langdetect import detect, DetectorFactory
from langdetect.lang_detect_exception import LangDetectException
from src.logging.logger import logging
from src.exception import CustomException

# Set seed for consistent results
DetectorFactory.seed = 0

class LanguageDetector:
    """
    Language detection utility with transliteration support for Indian languages.
    Provides language detection and transliteration guidance for voice AI responses.
    """
    
    def __init__(self):
        # Language mapping with transliteration patterns
        self.language_mapping = {
            'hi': {
                'name': 'Hindi',
                'code': 'hi',
                'transliteration_style': 'devanagari_roman',
                'sample_phrases': {
                    'greeting': '<PERSON><PERSON><PERSON>, main aapki madad kar sakta hun',
                    'error': 'Mujhe kuch dikkat ho rahi hai',
                    'help': 'Main aapki sahayata kar sakta hun'
                }
            },
            'bn': {
                'name': 'Bengali',
                'code': 'bn',
                'transliteration_style': 'bengali_roman',
                'sample_phrases': {
                    'greeting': '<PERSON><PERSON><PERSON>, ami apnake sahajjo korte pari',
                    'error': 'Amar kichhu somosya hocche',
                    'help': 'Ami apnake sahajjo korte pari'
                }
            },
            'ta': {
                'name': 'Tamil',
                'code': 'ta',
                'transliteration_style': 'tamil_roman',
                'sample_phrases': {
                    'greeting': 'Vanakkam, naan ungalukku uthavi seiya mudiyum',
                    'error': 'Enakku konjam problem irukku',
                    'help': 'Naan ungalukku uthavi seiya mudiyum'
                }
            },
            'te': {
                'name': 'Telugu',
                'code': 'te',
                'transliteration_style': 'telugu_roman',
                'sample_phrases': {
                    'greeting': 'Namaskaram, nenu mee sahayam cheyagalanu',
                    'error': 'Naaku konni samasyalu unnaayi',
                    'help': 'Nenu mee sahayam cheyagalanu'
                }
            },
            'mr': {
                'name': 'Marathi',
                'code': 'mr',
                'transliteration_style': 'marathi_roman',
                'sample_phrases': {
                    'greeting': 'Namaskar, mi tumhali madad karu shakto',
                    'error': 'Mala kahi samasya ahe',
                    'help': 'Mi tumhali madad karu shakto'
                }
            },
            'gu': {
                'name': 'Gujarati',
                'code': 'gu',
                'transliteration_style': 'gujarati_roman',
                'sample_phrases': {
                    'greeting': 'Namaste, hun tamari madad kari shaku chu',
                    'error': 'Mane koi samasya che',
                    'help': 'Hun tamari madad kari shaku chu'
                }
            },
            'pa': {
                'name': 'Punjabi',
                'code': 'pa',
                'transliteration_style': 'punjabi_roman',
                'sample_phrases': {
                    'greeting': 'Sat sri akal, main tuhadi madad kar sakda han',
                    'error': 'Manu koi samasya hai',
                    'help': 'Main tuhadi madad kar sakda han'
                }
            },
            'en': {
                'name': 'English',
                'code': 'en',
                'transliteration_style': 'english',
                'sample_phrases': {
                    'greeting': 'Hello, I can help you',
                    'error': 'I am experiencing some issues',
                    'help': 'I can assist you'
                }
            }
        }
        
        # Common Indian language codes that might be detected
        self.indian_languages = {'hi', 'bn', 'ta', 'te', 'mr', 'gu', 'pa', 'kn', 'ml', 'or', 'as'}
        
    def detect_language(self, text: str) -> Tuple[str, str, Dict]:
        """
        Detect language from input text and return language info.
        
        Args:
            text: Input text to analyze
            
        Returns:
            Tuple of (language_code, language_name, language_config)
        """
        try:
            if not text or len(text.strip()) < 3:
                # Default to English for very short text
                return self._get_language_info('en')
                
            # Clean text for better detection
            cleaned_text = text.strip().lower()
            
            # Detect language
            detected_lang = detect(cleaned_text)
            logging.info(f"Detected language: {detected_lang} for text: '{text[:50]}...'")
            
            return self._get_language_info(detected_lang)
            
        except LangDetectException as e:
            logging.warning(f"Language detection failed: {e}. Defaulting to English.")
            return self._get_language_info('en')
        except Exception as e:
            logging.error(f"Unexpected error in language detection: {e}")
            raise CustomException(e, sys)
    
    def _get_language_info(self, lang_code: str) -> Tuple[str, str, Dict]:
        """
        Get language information for a given language code.
        
        Args:
            lang_code: Language code (e.g., 'hi', 'en')
            
        Returns:
            Tuple of (language_code, language_name, language_config)
        """
        if lang_code in self.language_mapping:
            config = self.language_mapping[lang_code]
            return lang_code, config['name'], config
        else:
            # For unsupported languages, default to English
            logging.warning(f"Unsupported language code: {lang_code}. Defaulting to English.")
            config = self.language_mapping['en']
            return 'en', config['name'], config
    
    def get_transliteration_instructions(self, lang_code: str) -> str:
        """
        Get specific transliteration instructions for the detected language.
        
        Args:
            lang_code: Language code
            
        Returns:
            Detailed transliteration instructions for the LLM
        """
        if lang_code == 'en':
            return "Respond in clear, natural English."
        
        if lang_code in self.indian_languages:
            lang_info = self.language_mapping.get(lang_code, self.language_mapping['hi'])
            
            instructions = f"""
CRITICAL LANGUAGE INSTRUCTIONS for {lang_info['name']} ({lang_code}):

1. **MANDATORY TRANSLITERATION**: You MUST respond in {lang_info['name']} using English alphabet transliteration.
2. **PRONUNCIATION ACCURACY**: Use transliteration that matches natural {lang_info['name']} pronunciation patterns.
3. **NO ENGLISH MIXING**: Never mix English words. Use native {lang_info['name']} equivalents.
4. **NATURAL FLOW**: Make responses sound like a native {lang_info['name']} speaker, not machine-like.

EXAMPLE PHRASES for {lang_info['name']}:
- Greeting: "{lang_info['sample_phrases']['greeting']}"
- Error: "{lang_info['sample_phrases']['error']}"
- Help: "{lang_info['sample_phrases']['help']}"

TRANSLITERATION STYLE: {lang_info['transliteration_style']}
"""
            return instructions
        else:
            # For other languages, provide general guidance
            return f"""
Respond in {lang_code} language using appropriate transliteration if needed.
Maintain natural pronunciation patterns and avoid mixing with English.
"""
    
    def is_indian_language(self, lang_code: str) -> bool:
        """Check if the detected language is an Indian language."""
        return lang_code in self.indian_languages
    
    def get_supported_languages(self) -> Dict[str, str]:
        """Get list of supported languages."""
        return {code: info['name'] for code, info in self.language_mapping.items()}
