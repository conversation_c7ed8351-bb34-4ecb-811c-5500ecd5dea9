import os
import sys
from src.agent.agent import AgenticRAG  # your existing class
from src.logging.logger import logging
from src.exception import CustomException
from src.utils.language_detection import LanguageDetector
from src.tools.vector_database_tool import VectorDatabaseTool
from src.tools.web_search_tool import WebSearchTool

from livekit.agents import AgentSession, Agent, JobContext, llm
from livekit.plugins import langchain, deepgram, cartesia, silero,groq,speechify
from dotenv import load_dotenv

load_dotenv()
instructions=(
                "You are a sophisticated, multilingual voice AI assistant with advanced language detection and intelligent tool routing capabilities.\n\n"

                "**CRITICAL LANGUAGE HANDLING - ABSOLUTE PRIORITY:**\n"
                "1. **Automatic Language Detection:** The user's language is automatically detected from their input.\n"
                "2. **MANDATORY Language Consistency:** Once detected, you MUST maintain the SAME language throughout the ENTIRE conversation.\n"
                "3. **Supported Languages with Specific Handling:**\n"
                "   - **English (UK/US):** Respond in natural, clear English\n"
                "   - **German:** Respond in proper German with correct grammar and vocabulary\n"
                "   - **French:** Re<PERSON>ond in proper French with correct grammar and vocabulary\n"
                "   - **Hindi:** Use English alphabet transliteration (e.g., 'Namaste, main aapki madad kar sakta hun')\n"
                "   - **Tamil:** Use English alphabet transliteration (e.g., 'Vanakkam, naan ungalukku uthavi seiya mudiyum')\n"
                "   - **Telugu:** Use English alphabet transliteration (e.g., 'Namaskaram, nenu mee sahayam cheyagalanu')\n"
                "4. **ZERO Language Mixing:** Never mix English words in non-English responses. Use native language equivalents.\n"
                "5. **Native Speaker Quality:** Make responses sound natural and fluent, not machine-translated.\n"
                "6. **Language Context Processing:** When tools provide language context markers like '[LANGUAGE: Hindi - ...]', use that information to format your response appropriately.\n\n"

                "**INTELLIGENT TOOL ROUTING - CRITICAL RULES:**\n"
                "You have access to TWO distinct tools:\n\n"

                "🔍 **vector_database_search** - Use for:\n"
                "- Technical documentation (electrical machines, transformers, motors)\n"
                "- Internal knowledge and stored documents\n"
                "- Domain-specific information\n"
                "- Company policies and procedures\n"
                "- Historical data and specifications\n\n"

                "🌐 **web_search** - Use for:\n"
                "- Current news and recent events\n"
                "- Real-time information (weather, stock prices)\n"
                "- Breaking news and latest developments\n"
                "- Recent product releases\n"
                "- When vector database doesn't have relevant information\n\n"

                "**TOOL SELECTION PRIORITY:**\n"
                "1. For technical/domain questions → Try vector_database_search FIRST\n"
                "2. For current events/real-time data → Use web_search DIRECTLY\n"
                "3. If vector search returns insufficient results → Use web_search as fallback\n"
                "4. For simple greetings/basic conversation → Respond directly without tools\n\n"

                "**LANGUAGE-SPECIFIC RESPONSE EXAMPLES:**\n"
                "- **English:** 'Hello, I can help you with that' | Error: 'I'm experiencing some technical issues'\n"
                "- **German:** 'Hallo, ich kann Ihnen dabei helfen' | Error: 'Ich habe einige technische Probleme'\n"
                "- **French:** 'Bonjour, je peux vous aider avec cela' | Error: 'Je rencontre quelques problèmes techniques'\n"
                "- **Hindi:** 'Namaste, main aapki madad kar sakta hun' | Error: 'Mujhe kuch technical dikkat ho rahi hai'\n"
                "- **Tamil:** 'Vanakkam, naan ungalukku uthavi seiya mudiyum' | Error: 'Enakku konjam technical problem irukku'\n"
                "- **Telugu:** 'Namaskaram, nenu mee sahayam cheyagalanu' | Error: 'Naaku konni technical samasyalu unnaayi'\n\n"

                "**RESPONSE GUIDELINES:**\n"
                "- Keep responses natural and conversational for voice interaction\n"
                "- Use proper transliteration that sounds natural when spoken\n"
                "- Maintain consistent language throughout the session\n"
                "- Be concise but informative\n"
                "- Always prioritize user's detected language over English"
            ),
            

# Create your LangGraph workflow instance and tools
rag_agent = AgenticRAG()
language_detector = LanguageDetector()

# Global variables for session language tracking
session_language = None
session_language_config = None
session_language_instructions = None

def create_vector_database_tool():
    """
    Create a vector database search tool for internal knowledge retrieval.
    """
    # Initialize the vector database tool
    vector_tool = VectorDatabaseTool(rag_agent.llm)

    @llm.function_tool(
        name="vector_database_search",
        description="Search internal documents and knowledge base for technical information, specifications, and stored content"
    )
    async def vector_database_search(query: str) -> str:
        """
        Search internal vector database for technical documentation and stored knowledge.

        Use this tool for:
        - Technical questions (electrical machines, motors, generators, transformers)
        - Internal documentation and manuals
        - Domain-specific information
        - Company policies and procedures
        - Historical data and archived content
        - Previously stored research and documents

        Args:
            query: The user's question about internal knowledge or technical information

        Returns:
            Response from internal knowledge base
        """
        try:
            global session_language, session_language_config, session_language_instructions
            logging.info(f"Vector database tool invoked with query: '{query}'")

            # Detect language if not already set for session
            if session_language is None:
                lang_code, lang_name, lang_config = language_detector.detect_language(query)
                session_language = lang_code
                session_language_config = lang_config
                session_language_instructions = language_detector.get_transliteration_instructions(lang_code)
                logging.info(f"Language detected: {lang_name} ({lang_code})")
                logging.info(f"Language type: {language_detector.get_language_type(lang_code)}")

            # Search vector database with language context
            result = vector_tool.search_documents(query, session_language)

            if result['is_relevant']:
                response = result['results']
                logging.info(f"Vector database found relevant results")

                # Add language instruction to response if not English
                if session_language != 'en':
                    response_format = language_detector.get_response_format_instructions(session_language)
                    response = f"[LANGUAGE: {session_language_config['name']} - {response_format}]\n\n{response}"
            else:
                # Provide language-appropriate "no results" message
                if session_language == 'hi':
                    response = "Mere paas is sawal ka jawaab mere knowledge base mein nahi hai. Kripaya web search try kariye current information ke liye."
                elif session_language == 'ta':
                    response = "Enakku indha kelvikku ennoda knowledge base la relevant information illa. Current information kkaaga web search try pannunga."
                elif session_language == 'te':
                    response = "Naa knowledge base lo ee prashnaku sambandhinchinavi levu. Current information kosam web search try cheyandi."
                elif session_language == 'de':
                    response = "Ich habe keine relevanten Informationen in meiner Wissensdatenbank für diese Anfrage. Bitte versuchen Sie die Websuche für aktuelle Informationen."
                elif session_language == 'fr':
                    response = "Je n'ai pas d'informations pertinentes dans ma base de connaissances pour cette requête. Veuillez essayer la recherche web pour des informations actuelles."
                else:
                    response = "I don't have relevant information in my knowledge base for this query. Please try the web search for current information."
                logging.info(f"Vector database did not find relevant results")

            return response

        except Exception as e:
            logging.error(f"Vector database tool error: {e}")
            return "I encountered an issue searching the knowledge base. Please try rephrasing your question."

    return vector_database_search

def create_web_search_tool():
    """
    Create a web search tool for current and real-time information.
    """
    # Initialize the web search tool
    web_tool = WebSearchTool()

    @llm.function_tool(
        name="web_search",
        description="Search the internet for current news, real-time information, and recent events"
    )
    async def web_search(query: str) -> str:
        """
        Search the web for current information, news, and real-time data.

        Use this tool for:
        - Current news and recent events
        - Real-time information (weather, stock prices)
        - Breaking news and latest developments
        - Recent product releases or announcements
        - Current market data and live information
        - When vector database doesn't have relevant information

        Args:
            query: The user's question about current events or real-time information

        Returns:
            Current information from web search
        """
        try:
            global session_language, session_language_config, session_language_instructions
            logging.info(f"Web search tool invoked with query: '{query}'")

            # Detect language if not already set for session
            if session_language is None:
                lang_code, lang_name, lang_config = language_detector.detect_language(query)
                session_language = lang_code
                session_language_config = lang_config
                session_language_instructions = language_detector.get_transliteration_instructions(lang_code)
                logging.info(f"Language detected: {lang_name} ({lang_code})")

            # Use session language for web search
            result = web_tool.search_web(query, session_language)

            response = result['results']
            logging.info(f"Web search completed with {result['result_count']} results")

            # Add language instruction to response if not English
            if session_language != 'en':
                response_format = language_detector.get_response_format_instructions(session_language)
                response = f"[LANGUAGE: {session_language_config['name']} - {response_format}]\n\n{response}"

            return response

        except Exception as e:
            logging.error(f"Web search tool error: {e}")
            return "I encountered an issue searching the web. Please try rephrasing your question."

    return web_search

def get_dynamic_language_instructions(lang_code: str, lang_config: dict) -> str:
    """
    Generate dynamic language instructions based on detected language.
    """
    if lang_code == 'en':
        return "\n**CURRENT SESSION LANGUAGE:** English - Respond in clear, natural English."

    lang_name = lang_config.get('name', 'Unknown')
    lang_type = language_detector.get_language_type(lang_code)

    if lang_type == 'indian':
        return f"""
**CURRENT SESSION LANGUAGE:** {lang_name} ({lang_code})
**MANDATORY RESPONSE FORMAT:** Use English alphabet transliteration that sounds natural when spoken in {lang_name}.
**EXAMPLE GREETING:** "{lang_config['sample_phrases']['greeting']}"
**EXAMPLE ERROR:** "{lang_config['sample_phrases']['error']}"
**CRITICAL:** Never use English words - always use {lang_name} equivalents in transliteration.
"""
    elif lang_type == 'european':
        return f"""
**CURRENT SESSION LANGUAGE:** {lang_name} ({lang_code})
**MANDATORY RESPONSE FORMAT:** Respond in proper {lang_name} with correct grammar and native vocabulary.
**EXAMPLE GREETING:** "{lang_config['sample_phrases']['greeting']}"
**EXAMPLE ERROR:** "{lang_config['sample_phrases']['error']}"
**CRITICAL:** Use authentic {lang_name} - no English mixing allowed.
"""
    else:
        return f"""
**CURRENT SESSION LANGUAGE:** {lang_name} ({lang_code})
**RESPONSE FORMAT:** Maintain natural pronunciation patterns in {lang_name}.
"""

async def entrypoint(ctx: JobContext):
    try:
        await ctx.connect()  # Agent joins inbound call room automatically

        # Create the separate tools
        vector_tool = create_vector_database_tool()
        web_tool = create_web_search_tool()

        session = AgentSession(
            vad=silero.VAD.load(),
            stt=groq.STT(
                model="whisper-large-v3",
                language="en",
                de

            ),
             llm=groq.LLM(
                model="llama3-70b-8192",

            ),

            tts=speechify.TTS(
                model="simba-multilingual"),


        )

        # Create agent with both tools
        agent = Agent(
            llm=session.llm,
            instructions=instructions,
            tools=[vector_tool, web_tool]
        )

        await session.start(agent=agent, room=ctx.room)

        await session.generate_reply(instructions="Hello! How may I assist you today?")

    except Exception as e:
        logging.error(f"voice agent error: {e}")
        raise CustomException(e,sys)

if __name__ == "__main__":
    from livekit.agents import cli, WorkerOptions
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))
